@extends('layouts.app')
@section('content')
    <h1>แพ็กเกจ (จัดการหลังบ้าน)</h1>
    <a href="{{ route('admin.packages.create') }}" class="btn btn-primary mb-3">เพิ่มแพ็กเกจ</a>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ชื่อแพ็กเกจ</th>
                <th>รายละเอียด</th>
                <th>ราคา</th>
                <th>รูปภาพ</th>
                <th>จัดการ</th>
            </tr>
        </thead>
        <tbody>
            @foreach($packages as $package)
                <tr>
                    <td>{{ $package->name }}</td>
                    <td>{{ Str::limit($package->description, 50) }}</td>
                    <td>{{ number_format($package->price, 2) }}</td>
                    <td>@if($package->image)<img src="{{ asset('storage/'.$package->image) }}" style="max-width:80px;">@endif</td>
                    <td>
                        <a href="{{ route('admin.packages.edit', $package) }}" class="btn btn-sm btn-warning">แก้ไข</a>
                        <form action="{{ route('admin.packages.destroy', $package) }}" method="POST" style="display:inline-block;">
                            @csrf @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('ยืนยันลบ?')">ลบ</button>
                        </form>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
@endsection 