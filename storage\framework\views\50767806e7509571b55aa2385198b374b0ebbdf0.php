
<?php $__env->startSection('content'); ?>
    <h1>แพ็กเกจ (จัดการหลังบ้าน)</h1>
    <a href="<?php echo e(route('admin.packages.create')); ?>" class="btn btn-primary mb-3">เพิ่มแพ็กเกจ</a>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ชื่อแพ็กเกจ</th>
                <th>รายละเอียด</th>
                <th>ราคา</th>
                <th>รูปภาพ</th>
                <th>จัดการ</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($package->name); ?></td>
                    <td><?php echo e(Str::limit($package->description, 50)); ?></td>
                    <td><?php echo e(number_format($package->price, 2)); ?></td>
                    <td><?php if($package->image): ?><img src="<?php echo e(asset('storage/'.$package->image)); ?>" style="max-width:80px;"><?php endif; ?></td>
                    <td>
                        <a href="<?php echo e(route('admin.packages.edit', $package)); ?>" class="btn btn-sm btn-warning">แก้ไข</a>
                        <form action="<?php echo e(route('admin.packages.destroy', $package)); ?>" method="POST" style="display:inline-block;">
                            <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('ยืนยันลบ?')">ลบ</button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/packages/index.blade.php ENDPATH**/ ?>