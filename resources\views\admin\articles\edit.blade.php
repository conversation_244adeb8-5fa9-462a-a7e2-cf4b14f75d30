@extends('layouts.app')
@section('content')
    <h1>แก้ไขบทความ</h1>
    <form action="{{ route('admin.articles.update', $article) }}" method="POST" enctype="multipart/form-data">
        @csrf @method('PUT')
        <div class="mb-3">
            <label>หัวข้อ</label>
            <input type="text" name="title" class="form-control" value="{{ $article->title }}" required>
        </div>
        <div class="mb-3">
            <label>เนื้อหา</label>
            <textarea name="content" class="form-control">{{ $article->content }}</textarea>
        </div>
        <div class="mb-3">
            <label>วันที่เผยแพร่</label>
            <input type="date" name="published_at" class="form-control" value="{{ $article->published_at ? $article->published_at->format('Y-m-d') : '' }}">
        </div>
        <div class="mb-3">
            <label>รูปภาพ</label>
            @if($article->image)
                <img src="{{ asset('storage/'.$article->image) }}" style="max-width:120px;display:block;">
            @endif
            <input type="file" name="image" class="form-control">
        </div>
        <button type="submit" class="btn btn-success">บันทึก</button>
        <a href="{{ route('admin.articles.index') }}" class="btn btn-secondary">ย้อนกลับ</a>
    </form>
@endsection 