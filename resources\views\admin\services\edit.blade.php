@extends('layouts.app')
@section('content')
    <h1>แก้ไขบริการ</h1>
    <form action="{{ route('admin.services.update', $service) }}" method="POST" enctype="multipart/form-data">
        @csrf @method('PUT')
        <div class="mb-3">
            <label>ชื่อบริการ</label>
            <input type="text" name="name" class="form-control" value="{{ $service->name }}" required>
        </div>
        <div class="mb-3">
            <label>รายละเอียด</label>
            <textarea name="description" class="form-control">{{ $service->description }}</textarea>
        </div>
        <div class="mb-3">
            <label>ราคา</label>
            <input type="number" name="price" class="form-control" step="0.01" value="{{ $service->price }}">
        </div>
        <div class="mb-3">
            <label>รูปภาพ</label>
            @if($service->image)
                <img src="{{ asset('storage/'.$service->image) }}" style="max-width:120px;display:block;">
            @endif
            <input type="file" name="image" class="form-control">
        </div>
        <button type="submit" class="btn btn-success">บันทึก</button>
        <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">ย้อนกลับ</a>
    </form>
@endsection 