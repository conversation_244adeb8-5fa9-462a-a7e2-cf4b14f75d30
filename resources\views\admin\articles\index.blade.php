@extends('layouts.app')
@section('content')
    <h1>บทความ/ข่าวสาร (จัดการหลังบ้าน)</h1>
    <a href="{{ route('admin.articles.create') }}" class="btn btn-primary mb-3">เพิ่มบทความ</a>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>หัวข้อ</th>
                <th>เนื้อหา</th>
                <th>วันที่เผยแพร่</th>
                <th>รูปภาพ</th>
                <th>จัดการ</th>
            </tr>
        </thead>
        <tbody>
            @foreach($articles as $article)
                <tr>
                    <td>{{ $article->title }}</td>
                    <td>{{ Str::limit($article->content, 50) }}</td>
                    <td>{{ $article->published_at ? $article->published_at->format('d/m/Y') : '-' }}</td>
                    <td>@if($article->image)<img src="{{ asset('storage/'.$article->image) }}" style="max-width:80px;">@endif</td>
                    <td>
                        <a href="{{ route('admin.articles.edit', $article) }}" class="btn btn-sm btn-warning">แก้ไข</a>
                        <form action="{{ route('admin.articles.destroy', $article) }}" method="POST" style="display:inline-block;">
                            @csrf @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('ยืนยันลบ?')">ลบ</button>
                        </form>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
@endsection 