
<?php $__env->startSection('content'); ?>
    <h1>บทความ/ข่าวสาร (จัดการหลังบ้าน)</h1>
    <a href="<?php echo e(route('admin.articles.create')); ?>" class="btn btn-primary mb-3">เพิ่มบทความ</a>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>หัวข้อ</th>
                <th>เนื้อหา</th>
                <th>วันที่เผยแพร่</th>
                <th>รูปภาพ</th>
                <th>จัดการ</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $articles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td><?php echo e($article->title); ?></td>
                    <td><?php echo e(Str::limit($article->content, 50)); ?></td>
                    <td><?php echo e($article->published_at ? $article->published_at->format('d/m/Y') : '-'); ?></td>
                    <td><?php if($article->image): ?><img src="<?php echo e(asset('storage/'.$article->image)); ?>" style="max-width:80px;"><?php endif; ?></td>
                    <td>
                        <a href="<?php echo e(route('admin.articles.edit', $article)); ?>" class="btn btn-sm btn-warning">แก้ไข</a>
                        <form action="<?php echo e(route('admin.articles.destroy', $article)); ?>" method="POST" style="display:inline-block;">
                            <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('ยืนยันลบ?')">ลบ</button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/articles/index.blade.php ENDPATH**/ ?>