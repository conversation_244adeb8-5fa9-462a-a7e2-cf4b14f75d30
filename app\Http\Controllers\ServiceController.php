<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;

class ServiceController extends Controller
{
    // หน้าบ้าน: แสดงรายการบริการ
    public function index()
    {
        $services = Service::all();
        return view('services.index', compact('services'));
    }

    // หน้าบ้าน: แสดงรายละเอียดบริการ
    public function show(Service $service)
    {
        return view('services.show', compact('service'));
    }

    // หลังบ้าน: ฟอร์มสร้างบริการใหม่
    public function create()
    {
        return view('admin.services.create');
    }

    // หลังบ้าน: บันทึกบริการใหม่
    public function store(Request $request)
    {
        $data = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $imageErrors = ImageHelper::validateImage($request->file('image'));
            if (!empty($imageErrors)) {
                return back()->withErrors(['image' => $imageErrors])->withInput();
            }
            $data['image'] = ImageHelper::uploadAndResize($request->file('image'), 'services');
        }

        Service::create($data);
        return redirect()->route('admin.services.index')->with('success', 'เพิ่มบริการสำเร็จ');
    }

    // หลังบ้าน: ฟอร์มแก้ไขบริการ
    public function edit(Service $service)
    {
        return view('admin.services.edit', compact('service'));
    }

    // หลังบ้าน: อัปเดตบริการ
    public function update(Request $request, Service $service)
    {
        $data = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $imageErrors = ImageHelper::validateImage($request->file('image'));
            if (!empty($imageErrors)) {
                return back()->withErrors(['image' => $imageErrors])->withInput();
            }

            // Delete old image
            ImageHelper::deleteImage($service->image);

            // Upload new image
            $data['image'] = ImageHelper::uploadAndResize($request->file('image'), 'services');
        }

        $service->update($data);
        return redirect()->route('admin.services.index')->with('success', 'อัปเดตบริการสำเร็จ');
    }

    // หลังบ้าน: ลบบริการ
    public function destroy(Service $service)
    {
        // Delete associated image
        ImageHelper::deleteImage($service->image);

        $service->delete();
        return redirect()->route('admin.services.index')->with('success', 'ลบบริการสำเร็จ');
    }
} 