@extends('layouts.app')
@section('content')
    <h1>ข้อมูลติดต่อ (จัดการหลังบ้าน)</h1>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>ชื่อ</th>
                <th>เบอร์โทร</th>
                <th>อีเมล</th>
                <th>ที่อยู่</th>
                <th>ข้อความ</th>
                <th>จัดการ</th>
            </tr>
        </thead>
        <tbody>
            @foreach($contacts as $contact)
                <tr>
                    <td>{{ $contact->name }}</td>
                    <td>{{ $contact->phone }}</td>
                    <td>{{ $contact->email }}</td>
                    <td>{{ Str::limit($contact->address, 30) }}</td>
                    <td>{{ Str::limit($contact->message, 30) }}</td>
                    <td>
                        <a href="{{ route('admin.contacts.edit', $contact) }}" class="btn btn-sm btn-warning">แก้ไข</a>
                        <form action="{{ route('admin.contacts.destroy', $contact) }}" method="POST" style="display:inline-block;">
                            @csrf @method('DELETE')
                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('ยืนยันลบ?')">ลบ</button>
                        </form>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
@endsection 