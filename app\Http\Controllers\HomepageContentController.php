<?php

namespace App\Http\Controllers;

use App\Models\HomepageContent;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class HomepageContentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $contents = \App\Models\HomepageContent::all();
        return view('home', compact('contents'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\HomepageContent  $homepageContent
     * @return \Illuminate\Http\Response
     */
    public function show(HomepageContent $homepageContent)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\HomepageContent  $homepageContent
     * @return \Illuminate\Http\Response
     */
    public function edit(HomepageContent $homepageContent)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\HomepageContent  $homepageContent
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, HomepageContent $homepageContent)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\HomepageContent  $homepageContent
     * @return \Illuminate\Http\Response
     */
    public function destroy(HomepageContent $homepageContent)
    {
        //
    }
}
