@extends('layouts.app')
@section('content')
    <h1>แก้ไขข้อมูลติดต่อ</h1>
    <form action="{{ route('admin.contacts.update', $contact) }}" method="POST">
        @csrf @method('PUT')
        <div class="mb-3">
            <label>ชื่อ</label>
            <input type="text" name="name" class="form-control" value="{{ $contact->name }}">
        </div>
        <div class="mb-3">
            <label>เบอร์โทร</label>
            <input type="text" name="phone" class="form-control" value="{{ $contact->phone }}">
        </div>
        <div class="mb-3">
            <label>อีเมล</label>
            <input type="email" name="email" class="form-control" value="{{ $contact->email }}">
        </div>
        <div class="mb-3">
            <label>ที่อยู่</label>
            <textarea name="address" class="form-control">{{ $contact->address }}</textarea>
        </div>
        <div class="mb-3">
            <label>ข้อความ</label>
            <textarea name="message" class="form-control">{{ $contact->message }}</textarea>
        </div>
        <button type="submit" class="btn btn-success">บันทึก</button>
        <a href="{{ route('admin.contacts.index') }}" class="btn btn-secondary">ย้อนกลับ</a>
    </form>
@endsection 